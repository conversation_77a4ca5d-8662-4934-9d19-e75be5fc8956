#include<stdio.h>

int insert(int *ht,int size,int key){
    int hv;
    hv=key%size;
    if(ht[hv]==-1){
        ht[hv]=key;
        return 1;
    }
    return 0;
}

void display(int *ht,int size){
    int i;
    for(i=0;i<size;i++){
        if(ht[i]!=-1){
            printf("%d --> %d\n",i,ht[i]);
        }
    }
}

int delete(int *ht, int size, int key){
    int hv;
    hv=key%size;
    if(ht[hv]==key){
        ht[hv]=-1;
        return 1;
    }else{
        return 0;
    }
}

int search(int *ht, int size, int key){
    int hv;
    hv=key%size;

}

int main(){
    int *arr, size, i, ch, c=0, key, res;
    scanf("%d",&size);
    arr=(int*)malloc(size*sizeof(int));
    for(i=0; i<size; i++){
        arr[i]=-1;
    }
    while(1){
        printf("1.Insert 2.Delete 3.Search 4.Display 5.Exit :");
        scanf("%d",&ch);
        if(ch==1){
            //insert
            if(c==size){
                printf("it is full\n");
            
            }
            else{
                scanf("%d",&key);
                res=insert(arr,size,key);
                if(res==0){
                    printf("\n");
                }
                else{
                    printf("\n");
                    c++;
                }
            }
        }
        else if(ch==2){
            //delete
            if(c==0){
                printf("Hash table is empty");
            }
            else{
                scanf("%d",&key);
                res=delete(arr,size,key);
            }
            
        }
        else if(ch==3){
            //search
        }
        else if(ch==4){
            //display
            display(arr,size);
        }
        else if(ch==5){
            break;
            //break
        }
        else{
            
        }
    }
    return 0;
}