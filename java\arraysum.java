import java.util.*;
class arraysum {
    public static void main(String[] args) {
        Scanner s= new Scanner(System.in);
        System.out.println("Enter the value for size of the array");
        int n=s.nextInt();
        int[] a=new int[n];
        int i;
        for(i=0;i<n;i++){
            System.out.println("Enter element at index "+i);
            a[i]=s.nextInt();
        }    
        System.out.println("Entered elements are:");
        for(i=0;i<n;i++){
            System.out.println(a[i]);
        }
        int x=0;
        for(i=0;i<n;i++){
            x=x+a[i];
        }
        System.out.println("Sum of array is:"+x);     
    }
}
