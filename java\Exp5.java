public class Exp5{
    public static void main(String[] args) {
        rectangle r1= new rectangle();
        square s1= new square();

        System.out.println("Area of rectangle is: " + r1.area(5,4));
        System.out.println("Area of square is: " + s1.area(6));
    }
}

class Area {
    double area(int l, int b){
        return l*b;
    }
    double area(int l){
        return l*l;
    }
}

class rectangle extends Area{
    double area(int l, int b){
        return super.area(l);
    }
}

class square extends Area{
    
}

