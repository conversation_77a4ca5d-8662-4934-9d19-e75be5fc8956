#include<stdio.h>
#include<stdlib.h>

int insert(int *ht, int size,int key){
    int hv,i;
    hv=key%size;
    if(ht[hv]==-1){
        ht[hv]=key;
        return 1;
    }
    else{
        i=hv+1;
        while(1){
            if(ht[i]==-1){
                ht[i]=key;
                return 1;
            }
        }
    }
}





int main(){
    int *ht,size,count,ch,key,i,res;
    count=0;
    scanf("%d",&size);
    ht=(int*)malloc(size*sizeof(int));

    for(i=0;i<size;i++){
        ht[i]=-1;
    }

    while(1){
        printf("1.Insert\n2.Delete\n3.search\n4.Display\n5.Exit\n");
        scanf("%d",&ch);
        if (ch==1){
            //Insert
            if(count==size){
                printf("Hashtatable is full\n");
            }
            else{
                scanf("%d",&key);
                res=insert(ht,size,key);
                //0 1
                count++;
            }
        }
        else if (ch==2){
            //Delete
        }
        else if (ch==3){
            //Search            
        }
        else if (ch==4){
            //Display
            for(i=0;i<size;i++){
                printf("%d",ht[i]);
            }
                       
        }
        else if (ch==5){
            //exit
            break;
        }  
        else{
            printf("You entered an invalid option\n");
        }
    }
    return 0;
}