class Agecheck extends Exception {
    public Agecheck(String m){
        System.out.println();
        super(m);
    }
}

public class validate {
    public static void validate(int age) throws Agecheck{
        if(age<18){
            throw new Agecheck("Age must be above 18 to cast vote");
        }
    }
    public static void main(String[] args) {
        try {
            validate(12);
        } catch (Agecheck e) {
            System.out.println("Caught Exception"+e.getMessage());
        }
    }
}